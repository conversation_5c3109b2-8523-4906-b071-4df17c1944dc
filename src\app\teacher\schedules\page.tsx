"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  Calendar, 
  Plus, 
  Edit, 
  Trash2, 
  Clock,
  ArrowLeft,
  Filter,
  Search
} from "lucide-react"
import { Input } from "@/components/ui/input"

interface Schedule {
  id: number
  subject: string
  class: string
  room: string
  dayOfWeek: string
  startTime: string
  endTime: string
  status: 'active' | 'pending' | 'cancelled'
}

export default function TeacherSchedules() {
  const [schedules, setSchedules] = useState<Schedule[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const router = useRouter()

  useEffect(() => {
    // <PERSON><PERSON><PERSON> tra quyền truy cập
    const token = localStorage.getItem('token')
    if (!token) {
      router.push('/login')
      return
    }

    // Mock data
    setSchedules([
      {
        id: 1,
        subject: "Lập trình Java",
        class: "CNTT01",
        room: "A101",
        dayOfWeek: "Thứ 2",
        startTime: "07:00",
        endTime: "09:00",
        status: "active"
      },
      {
        id: 2,
        subject: "Cơ sở dữ liệu",
        class: "CNTT02",
        room: "B201",
        dayOfWeek: "Thứ 3",
        startTime: "09:00",
        endTime: "11:00",
        status: "pending"
      },
      {
        id: 3,
        subject: "Mạng máy tính",
        class: "CNTT01",
        room: "C301",
        dayOfWeek: "Thứ 5",
        startTime: "13:00",
        endTime: "15:00",
        status: "active"
      }
    ])
    setLoading(false)
  }, [router])

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-500">Đang hoạt động</Badge>
      case 'pending':
        return <Badge variant="secondary">Chờ duyệt</Badge>
      case 'cancelled':
        return <Badge variant="destructive">Đã hủy</Badge>
      default:
        return <Badge variant="outline">Không xác định</Badge>
    }
  }

  const filteredSchedules = schedules.filter(schedule =>
    schedule.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
    schedule.class.toLowerCase().includes(searchTerm.toLowerCase()) ||
    schedule.room.toLowerCase().includes(searchTerm.toLowerCase())
  )

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push('/teacher')}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Quay lại
              </Button>
              <div>
                <h1 className="text-xl font-semibold text-gray-900">
                  Quản lý lịch giảng
                </h1>
                <p className="text-sm text-gray-500">
                  Tạo và quản lý lịch giảng dạy của bạn
                </p>
              </div>
            </div>
            <Button onClick={() => router.push('/teacher/schedules/create')}>
              <Plus className="h-4 w-4 mr-2" />
              Tạo lịch mới
            </Button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Search and Filter */}
        <div className="mb-6 flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Tìm kiếm theo môn học, lớp, phòng..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <Button variant="outline">
            <Filter className="h-4 w-4 mr-2" />
            Bộ lọc
          </Button>
        </div>

        {/* Schedules Grid */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {filteredSchedules.map((schedule) => (
            <Card key={schedule.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">{schedule.subject}</CardTitle>
                  {getStatusBadge(schedule.status)}
                </div>
                <CardDescription>
                  Lớp {schedule.class} - Phòng {schedule.room}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex items-center text-sm text-gray-600">
                    <Calendar className="h-4 w-4 mr-2" />
                    {schedule.dayOfWeek}
                  </div>
                  <div className="flex items-center text-sm text-gray-600">
                    <Clock className="h-4 w-4 mr-2" />
                    {schedule.startTime} - {schedule.endTime}
                  </div>
                </div>
                <div className="flex space-x-2 mt-4">
                  <Button variant="outline" size="sm" className="flex-1">
                    <Edit className="h-4 w-4 mr-2" />
                    Sửa
                  </Button>
                  <Button variant="outline" size="sm" className="flex-1">
                    <Trash2 className="h-4 w-4 mr-2" />
                    Xóa
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredSchedules.length === 0 && (
          <div className="text-center py-12">
            <Calendar className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">
              Không có lịch giảng
            </h3>
            <p className="mt-1 text-sm text-gray-500">
              {searchTerm ? 'Không tìm thấy lịch giảng phù hợp.' : 'Bắt đầu bằng cách tạo lịch giảng mới.'}
            </p>
            {!searchTerm && (
              <div className="mt-6">
                <Button onClick={() => router.push('/teacher/schedules/create')}>
                  <Plus className="h-4 w-4 mr-2" />
                  Tạo lịch giảng đầu tiên
                </Button>
              </div>
            )}
          </div>
        )}
      </main>
    </div>
  )
}
