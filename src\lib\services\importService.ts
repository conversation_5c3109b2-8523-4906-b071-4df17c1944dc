import { apiCall } from '../utils'

export interface ImportStatus {
  type: string
  status: 'pending' | 'success' | 'error' | 'syncing'
  count: number
  lastSync: string
  message?: string
}

export interface ImportResult {
  count: number
  type: string
  message: string
  errors?: string[]
}

export interface ImportHistory {
  id: string
  type: string
  action: 'sync' | 'upload'
  status: 'success' | 'error'
  count: number
  fileName?: string
  timestamp: string
  message: string
  errors?: string[]
}

export interface SyncConfig {
  type: string
  enabled: boolean
  autoSync: boolean
  syncInterval: number // minutes
  sourceUrl?: string
  credentials?: {
    username: string
    password: string
  }
}

class ImportService {
  // Lấy trạng thái import của tất cả loại dữ liệu
  async getImportStatus(): Promise<ImportStatus[]> {
    const response = await apiCall('/import/status')
    return response.data
  }

  // Đồng bộ dữ liệu từ hệ thống nguồn
  async syncData(type: string): Promise<ImportResult> {
    const response = await apiCall(`/import/sync/${type}`, {
      method: 'POST'
    })
    return response.data
  }

  // Đồng bộ tất cả loại dữ liệu
  async syncAllData(): Promise<ImportResult[]> {
    const types = ['subjects', 'classes', 'teachers', 'rooms', 'academic_years']
    const results = []
    
    for (const type of types) {
      try {
        const result = await this.syncData(type)
        results.push(result)
      } catch (error) {
        results.push({
          type,
          count: 0,
          message: `Lỗi đồng bộ ${type}: ${error}`
        })
      }
    }
    
    return results
  }

  // Upload file import
  async uploadFile(file: File, type: string): Promise<ImportResult> {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('type', type)

    const response = await apiCall('/import/upload', {
      method: 'POST',
      headers: {}, // Remove Content-Type to let browser set it with boundary
      body: formData
    })
    return response.data
  }

  // Xóa dữ liệu
  async clearData(type: string): Promise<void> {
    await apiCall(`/import/clear/${type}`, {
      method: 'DELETE'
    })
  }

  // Kiểm tra kết nối với hệ thống nguồn
  async testConnection(): Promise<boolean> {
    try {
      const response = await apiCall('/import/test-connection')
      return response.data
    } catch {
      return false
    }
  }

  // Lấy lịch sử import
  async getImportHistory(type?: string): Promise<ImportHistory[]> {
    const endpoint = type ? `/import/history?type=${type}` : '/import/history'
    const response = await apiCall(endpoint)
    return response.data
  }

  // Lấy cấu hình đồng bộ
  async getSyncConfig(type: string): Promise<SyncConfig> {
    const response = await apiCall(`/import/config/${type}`)
    return response.data
  }

  // Cập nhật cấu hình đồng bộ
  async updateSyncConfig(type: string, config: Partial<SyncConfig>): Promise<SyncConfig> {
    const response = await apiCall(`/import/config/${type}`, {
      method: 'PUT',
      body: JSON.stringify(config)
    })
    return response.data
  }

  // Tải template import
  async downloadTemplate(type: string): Promise<Blob> {
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080/api'}/import/template/${type}`, {
      headers: {
        Authorization: `Bearer ${localStorage.getItem('token')}`
      }
    })
    
    if (!response.ok) {
      throw new Error('Không thể tải template')
    }
    
    return response.blob()
  }

  // Validate file trước khi upload
  validateFile(file: File, type: string): { valid: boolean; errors: string[] } {
    const errors: string[] = []
    
    // Kiểm tra định dạng file
    const allowedTypes = [
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'text/csv'
    ]
    
    if (!allowedTypes.includes(file.type)) {
      errors.push('Chỉ hỗ trợ file Excel (.xlsx, .xls) hoặc CSV')
    }
    
    // Kiểm tra kích thước file (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      errors.push('Kích thước file không được vượt quá 10MB')
    }
    
    return {
      valid: errors.length === 0,
      errors
    }
  }
}

export const importService = new ImportService()
