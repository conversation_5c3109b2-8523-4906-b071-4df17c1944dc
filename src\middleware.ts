import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // Các route không cần kiểm tra authentication
  const publicRoutes = ['/login', '/']
  
  // Kiểm tra nếu là route public
  if (publicRoutes.includes(pathname)) {
    return NextResponse.next()
  }

  // Kiểm tra token từ cookie hoặc header
  const token = request.cookies.get('token')?.value || 
                request.headers.get('authorization')?.replace('Bearer ', '')

  // Nếu không có token, chuyển về login
  if (!token) {
    return NextResponse.redirect(new URL('/login', request.url))
  }

  // Kiểm tra quyền truy cập dựa trên route
  if (pathname.startsWith('/admin')) {
    // Route admin - cần kiểm tra role admin
    const userInfo = request.cookies.get('userInfo')?.value
    if (userInfo) {
      try {
        const user = JSON.parse(userInfo)
        if (user.vaiTro !== 'ADMIN' && user.vaiTro !== 'QUAN_TRI') {
          // Không phải admin, chuyển về teacher dashboard
          return NextResponse.redirect(new URL('/teacher', request.url))
        }
      } catch (error) {
        // Lỗi parse, chuyển về login
        return NextResponse.redirect(new URL('/login', request.url))
      }
    }
  } else if (pathname.startsWith('/teacher')) {
    // Route teacher - cần kiểm tra role teacher
    const userInfo = request.cookies.get('userInfo')?.value
    if (userInfo) {
      try {
        const user = JSON.parse(userInfo)
        if (user.vaiTro === 'ADMIN' || user.vaiTro === 'QUAN_TRI') {
          // Là admin, chuyển về admin dashboard
          return NextResponse.redirect(new URL('/admin', request.url))
        }
      } catch (error) {
        // Lỗi parse, chuyển về login
        return NextResponse.redirect(new URL('/login', request.url))
      }
    }
  }

  // Xử lý các route cũ để redirect
  if (pathname === '/dashboard') {
    // Redirect dashboard cũ dựa trên role
    const userInfo = request.cookies.get('userInfo')?.value
    if (userInfo) {
      try {
        const user = JSON.parse(userInfo)
        if (user.vaiTro === 'ADMIN' || user.vaiTro === 'QUAN_TRI') {
          return NextResponse.redirect(new URL('/admin', request.url))
        } else {
          return NextResponse.redirect(new URL('/teacher', request.url))
        }
      } catch (error) {
        return NextResponse.redirect(new URL('/login', request.url))
      }
    }
  }

  // Redirect các route cũ khác
  const routeRedirects: { [key: string]: string } = {
    '/schedules': '/teacher/schedules',
    '/teaching-hours': '/teacher/teaching-hours',
    '/reports': '/teacher/reports',
    '/settings': '/teacher/settings',
    '/import': '/admin/import-management'
  }

  if (routeRedirects[pathname]) {
    return NextResponse.redirect(new URL(routeRedirects[pathname], request.url))
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
}
