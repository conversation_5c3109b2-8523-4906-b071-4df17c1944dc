"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  Calendar,
  Clock,
  FileText,
  Settings,
  LogOut,
  GraduationCap,
  BookOpen,
  BarChart3,
  User
} from "lucide-react"

interface UserInfo {
  maCanBo: string
  tenCanBo: string
  email: string
  soDienThoai: string
  vaiTro: string
}

interface TeacherStats {
  totalSchedules: number
  totalHours: number
  thisWeekHours: number
  pendingSchedules: number
}

export default function TeacherDashboard() {
  const [userInfo, setUserInfo] = useState<UserInfo>({
    maCanBo: '',
    tenCanBo: '',
    email: '',
    soDienThoai: '',
    vaiTro: ''
  })
  const [stats, setStats] = useState<TeacherStats | null>(null)
  const router = useRouter()

  useEffect(() => {
    // <PERSON><PERSON><PERSON> tra token và quyền truy cập
    const token = localStorage.getItem('token')
    if (!token) {
      router.push('/login')
      return
    }

    // Lấy thông tin user từ localStorage
    const userInfoStr = localStorage.getItem('userInfo')
    if (userInfoStr && userInfoStr !== 'undefined') {
      try {
        const user = JSON.parse(userInfoStr)
        setUserInfo(user)

        // Kiểm tra quyền truy cập - chỉ giáo viên mới được vào
        if (user.vaiTro === 'Admin' || user.vaiTro === 'ADMIN' || user.vaiTro === 'QUAN_TRI') {
          router.push('/admin')
          return
        }
      } catch (error) {
        console.error('Error parsing user info:', error)
        localStorage.removeItem('userInfo')
        localStorage.removeItem('token')
        router.push('/login')
        return
      }
    } else {
      router.push('/login')
      return
    }

    // Lấy thống kê giáo viên (mock data)
    setStats({
      totalSchedules: 45,
      totalHours: 120,
      thisWeekHours: 18,
      pendingSchedules: 3
    })
  }, [router])

  const handleLogout = () => {
    localStorage.removeItem('token')
    localStorage.removeItem('userInfo')
    router.push('/login')
  }

  const menuItems = [
    {
      title: 'Quản lý lịch giảng',
      description: 'Tạo và quản lý lịch giảng dạy',
      icon: Calendar,
      href: '/teacher/schedules',
      color: 'bg-blue-500'
    },
    {
      title: 'Giờ giảng dạy',
      description: 'Xem và tính toán giờ giảng',
      icon: Clock,
      href: '/teacher/teaching-hours',
      color: 'bg-green-500'
    },
    {
      title: 'Báo cáo',
      description: 'Xuất báo cáo Excel và PDF',
      icon: FileText,
      href: '/teacher/reports',
      color: 'bg-purple-500'
    },
    {
      title: 'Cài đặt',
      description: 'Thay đổi mật khẩu và thông tin',
      icon: Settings,
      href: '/teacher/settings',
      color: 'bg-gray-500'
    }
  ]

  if (!userInfo.tenCanBo) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <GraduationCap className="h-8 w-8 text-blue-600 mr-3" />
              <h1 className="text-xl font-semibold text-gray-900">
                Hệ thống quản lý lịch giảng - Giáo viên
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <User className="h-4 w-4 text-gray-500" />
                <span className="text-sm text-gray-700">
                  <strong>{userInfo.tenCanBo}</strong>
                </span>
                <Badge variant="secondary">{userInfo.vaiTro}</Badge>
              </div>
              <Button variant="outline" size="sm" onClick={handleLogout}>
                <LogOut className="h-4 w-4 mr-2" />
                Đăng xuất
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            Chào mừng, {userInfo.tenCanBo}!
          </h2>
          <p className="text-gray-600">
            Quản lý lịch giảng và theo dõi giờ giảng dạy của bạn
          </p>
        </div>

        {/* Stats Cards */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Tổng lịch giảng</CardTitle>
                <Calendar className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalSchedules}</div>
                <p className="text-xs text-muted-foreground">
                  Trong học kỳ này
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Tổng giờ giảng</CardTitle>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalHours}</div>
                <p className="text-xs text-muted-foreground">
                  Giờ đã giảng
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Tuần này</CardTitle>
                <BarChart3 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.thisWeekHours}</div>
                <p className="text-xs text-muted-foreground">
                  Giờ giảng tuần này
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Chờ xử lý</CardTitle>
                <BookOpen className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.pendingSchedules}</div>
                <p className="text-xs text-muted-foreground">
                  Lịch chờ duyệt
                </p>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {menuItems.map((item, index) => (
            <Card key={index} className="hover:shadow-lg transition-shadow cursor-pointer">
              <CardHeader>
                <div className={`w-12 h-12 ${item.color} rounded-lg flex items-center justify-center mb-4`}>
                  <item.icon className="h-6 w-6 text-white" />
                </div>
                <CardTitle className="text-lg">{item.title}</CardTitle>
                <CardDescription>{item.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <Button
                  className="w-full"
                  onClick={() => router.push(item.href)}
                >
                  Truy cập
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </main>
    </div>
  )
}
