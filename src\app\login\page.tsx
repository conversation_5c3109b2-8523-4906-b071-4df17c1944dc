'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, GraduationCap } from 'lucide-react'

export default function LoginPage() {
  const [formData, setFormData] = useState({
    maCanBo: '',
    matKhau: ''
  })
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const router = useRouter()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError('')

    try {
      const response = await fetch('http://localhost:8080/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      const data = await response.json()

      if (data.success) {
        // Lưu token vào localStorage
        localStorage.setItem('token', data.data.accessToken)

        // Lưu thông tin user, đảm bảo có dữ liệu hợp lệ
        const userInfo = data.data.teacherInfo || {
          maCanBo: formData.maCanBo,
          tenCanBo: 'User',
          email: '',
          soDienThoai: '',
          vaiTro: 'GIANG_VIEN'
        }
        localStorage.setItem('userInfo', JSON.stringify(userInfo))

        // Chuyển hướng đến dashboard
        router.push('/dashboard')
      } else {
        setError(data.message || 'Đăng nhập thất bại')
      }
    } catch (err) {
      setError('Lỗi kết nối đến server')
    } finally {
      setIsLoading(false)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <div className="flex items-center justify-center mb-4">
            <GraduationCap className="h-12 w-12 text-blue-600" />
          </div>
          <CardTitle className="text-2xl text-center">Đăng nhập</CardTitle>
          <CardDescription className="text-center">
            Hệ thống quản lý lịch giảng
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="maCanBo">Mã cán bộ</Label>
              <Input
                id="maCanBo"
                name="maCanBo"
                type="text"
                placeholder="Nhập mã cán bộ"
                value={formData.maCanBo}
                onChange={handleInputChange}
                required
                disabled={isLoading}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="matKhau">Mật khẩu</Label>
              <Input
                id="matKhau"
                name="matKhau"
                type="password"
                placeholder="Nhập mật khẩu"
                value={formData.matKhau}
                onChange={handleInputChange}
                required
                disabled={isLoading}
              />
            </div>

            {error && (
              <Alert variant="destructive">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <Button type="submit" className="w-full" disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Đang đăng nhập...
                </>
              ) : (
                'Đăng nhập'
              )}
            </Button>
          </form>

          <div className="mt-6 text-center text-sm text-gray-600">
            <p>Tài khoản mặc định:</p>
            <div className="mt-2 space-y-1">
              <p><strong>Admin:</strong> admin / 123456</p>
              <p><strong>Trưởng khoa:</strong> truongkhoa / 123456</p>
              <p><strong>Giảng viên:</strong> giangvien / 123456</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
