# 🏗️ Hệ Thống Quản Lý Lịch Giảng - Kiến Trúc <PERSON>

## 📋 Tổng Quan

Hệ thống đã được tái cấu trúc hoàn toàn để phân biệt rõ ràng giữa **Admin** và **Giảng viên**, với các chức năng và giao diện riêng biệt.

## 🔐 Hệ Thống Phân Quyền

### 🛡️ **Admin/Quản trị viên**
- **Vai trò**: `Admin`, `ADMIN`, `QUAN_TRI`
- **Route**: `/admin/*`
- **Màu chủ đạo**: Xanh dương
- **Icon**: Shield

### 👨‍🏫 **Giảng viên**
- **Vai trò**: Tất cả vai trò khác (không phải Admin)
- **Route**: `/teacher/*`
- **Màu chủ đạo**: <PERSON><PERSON><PERSON> lá
- **Icon**: User

## 🏗️ Cấu Trúc <PERSON>h<PERSON>

```
src/app/
├── admin/                    # 🛡️ ADMIN ONLY
│   ├── layout.tsx           # Layout riêng cho admin
│   ├── page.tsx             # Dashboard admin
│   ├── academic-years/      # Quản lý năm học
│   ├── departments/         # Quản lý khoa
│   ├── import-management/   # Import/Export dữ liệu
│   └── semesters/          # Quản lý học kỳ
│
├── teacher/                 # 👨‍🏫 TEACHER ONLY
│   ├── layout.tsx          # Layout riêng cho teacher
│   ├── page.tsx            # Dashboard teacher
│   ├── schedules/          # Quản lý lịch giảng
│   │   ├── page.tsx        # Danh sách lịch
│   │   └── create/         # Tạo lịch mới
│   ├── teaching-hours/     # Quản lý giờ giảng
│   ├── reports/            # Báo cáo
│   └── settings/           # Cài đặt cá nhân
│
├── login/                  # 🔑 Trang đăng nhập chung
├── page.tsx               # 🏠 Trang chủ với logic phân quyền
└── middleware.ts          # 🔒 Middleware kiểm tra quyền
```

## 🔄 Luồng Hoạt Động

### 1. **Đăng nhập**
```
/login → API Response → Phân tích role → Redirect
```

### 2. **Phân quyền tự động**
```
Admin role → /admin
Teacher role → /teacher
```

### 3. **Bảo vệ route**
```
Middleware → Kiểm tra token → Kiểm tra role → Allow/Redirect
```

## 🎨 Giao Diện

### **Admin Dashboard**
- **Header**: Xanh dương với icon Shield
- **Chức năng chính**:
  - Quản lý danh mục (Khoa, Năm học, Học kỳ)
  - Import/Export dữ liệu
  - Quản lý hệ thống

### **Teacher Dashboard**
- **Header**: Xanh lá với icon User
- **Chức năng chính**:
  - Quản lý lịch giảng cá nhân
  - Theo dõi giờ giảng
  - Báo cáo và thống kê
  - Cài đặt tài khoản

## 🔧 API Integration

### **Login Response Structure**
```json
{
  "success": true,
  "data": {
    "token": "jwt_token",
    "userInfo": {
      "id": 1,
      "maCanBo": "admin",
      "ten": "Quản trị viên",
      "email": "<EMAIL>",
      "sdt": "0123456789",
      "tenVaiTro": "Admin",
      "tenKhoa": "Công nghệ thông tin",
      "nu": false
    }
  }
}
```

### **Role Mapping**
- `tenVaiTro: "Admin"` → Admin Dashboard
- `tenVaiTro: "Giảng viên"` → Teacher Dashboard

## 🛡️ Security Features

### **Middleware Protection**
- ✅ Token validation
- ✅ Role-based access control
- ✅ Automatic redirects
- ✅ Route protection

### **Authentication**
- ✅ JWT token storage (localStorage + cookies)
- ✅ User info caching
- ✅ Session management
- ✅ Logout functionality

## 🚀 Deployment

### **Development**
```bash
npm run dev
```

### **Production**
```bash
npm run build
npm start
```

## 📱 Responsive Design

- ✅ Mobile-first approach
- ✅ Tablet optimization
- ✅ Desktop enhancement
- ✅ Cross-browser compatibility

## 🔍 Testing

### **Manual Testing Checklist**

#### **Admin Flow**
- [ ] Login với role Admin
- [ ] Redirect tự động đến `/admin`
- [ ] Truy cập các trang admin
- [ ] Không thể truy cập `/teacher/*`

#### **Teacher Flow**
- [ ] Login với role Giảng viên
- [ ] Redirect tự động đến `/teacher`
- [ ] Truy cập các trang teacher
- [ ] Không thể truy cập `/admin/*`

#### **Security**
- [ ] Truy cập route không có token → Redirect `/login`
- [ ] Truy cập sai quyền → Redirect đúng dashboard
- [ ] Logout → Clear session và redirect `/login`

## 🐛 Troubleshooting

### **Common Issues**

1. **Lỗi 404 khi truy cập route**
   - Kiểm tra middleware
   - Verify token và userInfo

2. **Redirect loop**
   - Clear localStorage
   - Check role mapping

3. **Layout không hiển thị**
   - Verify userInfo structure
   - Check role conditions

## 📈 Future Enhancements

- [ ] Real-time notifications
- [ ] Advanced reporting
- [ ] Mobile app
- [ ] API documentation
- [ ] Unit tests
- [ ] E2E tests

## 👥 Team

- **Frontend**: Next.js 15 + TypeScript
- **UI**: Tailwind CSS + shadcn/ui
- **State**: React Hooks
- **Auth**: JWT + Middleware

---

**📞 Support**: Liên hệ team phát triển nếu có vấn đề!
