"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { 
  GraduationCap, 
  LogOut, 
  User
} from "lucide-react"
import { Badge } from "@/components/ui/badge"

interface UserInfo {
  maCanBo: string
  tenCanBo: string
  email: string
  soDienThoai: string
  vaiTro: string
}

export default function TeacherLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null)
  const router = useRouter()

  useEffect(() => {
    // Kiểm tra token và quyền truy cập
    const token = localStorage.getItem('token')
    if (!token) {
      router.push('/login')
      return
    }

    // Lấy thông tin user từ localStorage
    const userInfoStr = localStorage.getItem('userInfo')
    if (userInfoStr && userInfoStr !== 'undefined') {
      try {
        const user = JSON.parse(userInfoStr)
        
        // <PERSON><PERSON><PERSON> tra quyền teacher (không phải admin)
        if (user.vaiTro === 'ADMIN' || user.vaiTro === 'QUAN_TRI') {
          router.push('/admin')
          return
        }
        
        setUserInfo(user)
      } catch (error) {
        console.error('Error parsing user info:', error)
        localStorage.removeItem('userInfo')
        localStorage.removeItem('token')
        router.push('/login')
        return
      }
    } else {
      router.push('/login')
      return
    }
  }, [router])

  const handleLogout = () => {
    localStorage.removeItem('token')
    localStorage.removeItem('userInfo')
    // Xóa cookies
    document.cookie = 'token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT'
    document.cookie = 'userInfo=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT'
    router.push('/login')
  }

  if (!userInfo) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <GraduationCap className="h-8 w-8 text-green-600 mr-3" />
              <div>
                <h1 className="text-xl font-semibold text-gray-900">
                  Hệ thống quản lý lịch giảng
                </h1>
                <p className="text-xs text-gray-500">
                  Dành cho giảng viên
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <User className="h-4 w-4 text-green-600" />
                <span className="text-sm text-gray-700">
                  <strong>{userInfo.tenCanBo}</strong>
                </span>
                <Badge variant="secondary" className="bg-green-100 text-green-800">
                  Giảng viên
                </Badge>
              </div>
              <Button variant="outline" size="sm" onClick={handleLogout}>
                <LogOut className="h-4 w-4 mr-2" />
                Đăng xuất
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      {children}
    </div>
  )
}
